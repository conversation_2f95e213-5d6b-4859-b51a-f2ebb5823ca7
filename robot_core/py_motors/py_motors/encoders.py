#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import RPi.GPIO as GPIO
import time
from collections import deque
import sys
import math
import statistics

# ========== KONFIGURACJA ==========
ENC_A, ENC_B = 16, 17          # Piny GPIO
ENC_PPR = 341.2                # Impulsów/obrót (Hall sensor)
GEAR_RATIO = 34                # Przełożenie reduktora
WHEEL_DIAMETER_MM = 70         # Średnica koła
TARGET_RPM = 15                # Oczekiwane RPM na kole (do walidacji)

# ========== OPTYMALIZACJA ==========
IMPULSE_HISTORY = 10           # Rozmiar bufora czasów impulsów
RPM_FILTER_WINDOW = 15         # Okno filtracji RPM
BOUNCETIME = 2                 # ms - dla enkoderów Halla
MIN_TIME_DIFF = 0.0001         # Minimalny czas między impulsami (zabezpieczenie)

# ========== ZMIENNE GLOBALNE ==========
impulse_buffer = deque(maxlen=IMPULSE_HISTORY)
rpm_window = deque(maxlen=RPM_FILTER_WINDOW)
direction = 1
last_display = time.time()
smoothed_rpm = 0.0

def setup():
    GPIO.setmode(GPIO.BCM)
    GPIO.setup(ENC_A, GPIO.IN, pull_up_down=GPIO.PUD_UP)
    GPIO.setup(ENC_B, GPIO.IN, pull_up_down=GPIO.PUD_UP)
    print(f"\nOczekiwane RPM silnika przy {TARGET_RPM}RPM na kole: {TARGET_RPM*GEAR_RATIO:.1f} RPM\n")

def encoder_callback(channel):
    global direction
    A, B = GPIO.input(ENC_A), GPIO.input(ENC_B)
    
    # Detekcja kierunku z zabezpieczeniem
    if channel == ENC_A:
        direction = 1 if (A != B) else -1
    else:
        direction = 1 if (A == B) else -1
    
    impulse_buffer.append(time.time())

def calculate_smoothed_rpm():
    if len(impulse_buffer) < 2:
        return 0.0
    
    # Oblicz różnice czasów i filtruj medianą
    time_diffs = [impulse_buffer[i+1]-impulse_buffer[i] 
                for i in range(len(impulse_buffer)-1)]
    median_diff = statistics.median(time_diffs)
    
    if median_diff > MIN_TIME_DIFF:
        motor_rpm = (60 / (ENC_PPR * median_diff)) * direction
        wheel_rpm = motor_rpm / GEAR_RATIO
        
        # Filtracja i walidacja
        if 0 < abs(wheel_rpm) < 100:  # Realistyczny zakres
            rpm_window.append(wheel_rpm)
            
            # Oblicz średnią ważoną (nowsze pomiary mają większą wagę)
            weights = range(1, len(rpm_window)+1)
            weighted_avg = sum(w*r for w,r in zip(weights, rpm_window)) / sum(weights)
            return weighted_avg
    
    return 0.0

def main():
    global smoothed_rpm, last_display
    
    try:
        setup()
        GPIO.add_event_detect(ENC_A, GPIO.BOTH, callback=encoder_callback, bouncetime=BOUNCETIME)
        GPIO.add_event_detect(ENC_B, GPIO.BOTH, callback=encoder_callback, bouncetime=BOUNCETIME)
        
        print("Czekam na dane z enkodera...")
        
        while True:
            # Aktualizacja RPM co 100ms
            if time.time() - last_display >= 0.1:
                smoothed_rpm = calculate_smoothed_rpm()
                motor_rpm = smoothed_rpm * GEAR_RATIO
                
                # Oblicz prędkość liniową
                speed_kmh = abs(smoothed_rpm) * (math.pi * WHEEL_DIAMETER_MM/1e6) * 60
                
                # Wyświetl z wyróżnieniem różnicy
                diff = (smoothed_rpm - TARGET_RPM)/TARGET_RPM*100
                diff_str = f"(Δ{diff:+.1f}%)" if abs(diff) > 5 else ""
                
                sys.stdout.write(
                    f"\rKolo: {smoothed_rpm:5.2f}RPM {diff_str} | "
                    f"Silnik: {motor_rpm:6.1f}RPM | "
                    f"Prędkość: {speed_kmh:.2f}km/h | "
                    f"Kierunek: {'↑' if direction>0 else '↓'}"
                )
                last_display = time.time()
            
            time.sleep(0.01)
            
    except KeyboardInterrupt:
        print("\nZamykanie...")
    finally:
        GPIO.cleanup()

if __name__ == "__main__":
    main()